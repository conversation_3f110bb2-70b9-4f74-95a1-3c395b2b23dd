#!/usr/bin/env python3
"""
监督Agent

负责监督测试用例执行过程中的应用状态和执行限制
"""

import time
from datetime import datetime
from typing import Tuple

from loguru import logger

from src.domain.ui_task.mobile.android.adb_connection_manager import is_device_available
from src.domain.ui_task.mobile.android.native_adb_utils import NativeADBUtils
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler
from config.globalconfig import get_or_create_settings_ins


class SupervisorAgent:
    """监督Agent - 负责应用状态监督和执行限制"""

    def __init__(self):
        pass

    def supervise_execution(self, state: DeploymentState, app_package: str, app_foreground_check: bool = True) -> DeploymentState:
        """
        监督执行过程，检查应用状态和执行限制
        注意：这是在执行过程中的监督，不是初始检查

        Args:
            state: 当前状态
            app_package: 应用包名
            app_foreground_check: 是否检查app在前台，默认True。有些跳转出去验证情况需要忽略检查

        Returns:
            更新后的状态
        """
        device = state.get("device")

        # 检查是否是第一次执行（如果没有执行历史，说明是第一次，不需要检查应用前台状态）
        history = state.get("history", [])
        # 只计算实际的决策+执行动作，不计算包装性记录
        execution_actions = [h for h in history if h.get("action") == "enhanced_get_location"]
        is_first_execution = len(execution_actions) == 0

        task_id = state["task_id"]

        # 1. 每轮执行都进行完整的检查流程，防止执行过程中设备断开或app被关闭
        execution_round = "first" if is_first_execution else "subsequent"
        logger.info(f"[task_id: {task_id}] 🔍 {execution_round.capitalize()} execution, performing complete checks")

        # 1.1 检查设备连接状态（每轮都检查）
        device_connected = self.check_device_connection(device, task_id)
        if not device_connected:
            error_message = f"Device {device} is not connected or not accessible"
            logger.error(f"[task_id: {task_id}] ❌ Device {device} is not connected, task failed")

            # 记录监督agent终止日志到execution_log
            self._log_supervisor_termination(task_id, "设备连接失败", error_message)

            state["completed"] = True
            state["execution_status"] = "failed"
            state["error_message"] = error_message
            state["history"].append({
                "action": "supervisor_device_check",
                "device": device,
                "execution_round": execution_round,
                "reason": "device_not_connected",
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            })
            return state

        # 1.2 检查应用是否已安装（每轮都检查）
        app_installed = self._check_app_installed(device, app_package, task_id)
        if not app_installed:
            error_msg = f"App {app_package} is not installed on device {device}"
            logger.error(f"[task_id: {task_id}] ❌ {error_msg}")

            # 记录监督agent终止日志到execution_log
            self._log_supervisor_termination(task_id, "应用未安装", error_msg)

            state["completed"] = True
            state["execution_status"] = "failed"
            state["error_message"] = error_msg
            state["history"].append({
                "action": "supervisor_app_install_check",
                "app_package": app_package,
                "execution_round": execution_round,
                "reason": "app_not_installed",
                "error_message": error_msg,
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            })
            return state

        # 1.3 第一次执行时，根据is_restart参数决定是否重启app确保回到首页状态
        app_restarted_successfully = False  # 标记app是否已经成功重启
        if is_first_execution:
            is_restart = state.get("is_restart", False)

            if is_restart:
                logger.info(
                    f"[task_id: {task_id}] 🔄 First execution with restart enabled, restarting app to ensure clean state")
                restart_success, restart_message = self._restart_app_to_home(device, app_package, task_id)

                if restart_success:
                    logger.info(f"[task_id: {task_id}] ✓ App restarted successfully: {restart_message}")
                    app_restarted_successfully = True  # 标记重启成功
                    state["history"].append({
                        "action": "supervisor_app_restart",
                        "app_package": app_package,
                        "execution_round": execution_round,
                        "message": restart_message,
                        "timestamp": datetime.now().isoformat(),
                        "status": "success"
                    })
                else:
                    error_msg = f"Failed to restart app {app_package}: {restart_message}"
                    logger.error(f"[task_id: {task_id}] ❌ {error_msg}")

                    # 记录监督agent终止日志到execution_log
                    self._log_supervisor_termination(task_id, "应用重启失败", error_msg)

                    state["completed"] = True
                    state["execution_status"] = "failed"
                    state["error_message"] = error_msg
                    state["history"].append({
                        "action": "supervisor_app_restart",
                        "app_package": app_package,
                        "execution_round": execution_round,
                        "reason": "app_restart_failed",
                        "error_message": error_msg,
                        "timestamp": datetime.now().isoformat(),
                        "status": "error"
                    })
                    return state
            else:
                logger.info(f"[task_id: {task_id}] ℹ️ First execution with restart disabled, skipping app restart")
                state["history"].append({
                    "action": "supervisor_app_restart_skipped",
                    "app_package": app_package,
                    "execution_round": execution_round,
                    "message": "App restart skipped due to is_restart=False",
                    "timestamp": datetime.now().isoformat(),
                    "status": "skipped"
                })

        # 1.4 检查应用是否在前台，如果不在则启动
        # 第一次启动时：
        #   - 如果已经重启成功，跳过前台检查（避免重复启动）
        #   - 如果没有重启或重启失败，需要检测并拉起app
        # 非第一次启动时：根据app_foreground_check参数决定是否检查前台状态
        should_check_foreground = (is_first_execution and not app_restarted_successfully) or (not is_first_execution and app_foreground_check)

        if should_check_foreground:
            current_app = self._get_foreground_app(device, task_id)
            if current_app and app_package in current_app:
                logger.info(f"[task_id: {task_id}] ✓ App {app_package} is in foreground")
                state["history"].append({
                    "action": "supervisor_app_foreground_check",
                    "app_package": app_package,
                    "execution_round": execution_round,
                    "message": f"App {app_package} is in foreground",
                    "timestamp": datetime.now().isoformat(),
                    "status": "success"
                })
            else:
                # 应用不在前台，尝试启动
                logger.info(f"[task_id: {task_id}] 🚀 App {app_package} not in foreground, starting app")
                # 记录应用启动开始的系统日志
                try:
                    from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
                    start_log = ExecutionLogService.create_system_log(f"应用不在前台，开始启动: {app_package}")
                    task_persistence_service.append_execution_log_entries(task_id, [start_log])
                except ImportError:
                    logger.debug(f"[{task_id}] Skip app start log due to import issue")

                success, method = self._switch_to_target_app(device, app_package, task_id)

                if success:
                    # 等待应用启动
                    time.sleep(10)

                    # 再次检查应用是否在前台
                    current_app = self._get_foreground_app(device, task_id)
                    if current_app and app_package in current_app:
                        success_msg = f"Successfully started app {app_package} using {method}"
                        logger.info(f"[task_id: {task_id}] ✓ {success_msg}")
                        # 记录应用启动成功的系统日志
                        try:
                            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
                            success_log = ExecutionLogService.create_system_log(f"应用启动成功: {app_package} (方法: {method})")
                            task_persistence_service.append_execution_log_entries(task_id, [success_log])
                        except ImportError:
                            logger.debug(f"[{task_id}] Skip app success log due to import issue")

                        state["history"].append({
                            "action": "supervisor_app_start",
                            "app_package": app_package,
                            "execution_round": execution_round,
                            "method": method,
                            "message": success_msg,
                            "timestamp": datetime.now().isoformat(),
                            "status": "success"
                        })
                    else:
                        warning_msg = f"App {app_package} started but not in foreground"
                        logger.warning(f"[task_id: {task_id}] ⚠️ {warning_msg}")

                        state["history"].append({
                            "action": "supervisor_app_start",
                            "app_package": app_package,
                            "execution_round": execution_round,
                            "method": method,
                            "message": warning_msg,
                            "timestamp": datetime.now().isoformat(),
                            "status": "warning"
                        })
                else:
                    error_msg = f"Failed to start app {app_package}: {method}"
                    logger.error(f"[task_id: {task_id}] ❌ {error_msg}")

                    # 记录监督agent终止日志到execution_log
                    self._log_supervisor_termination(task_id, "应用启动失败", error_msg)

                    state["completed"] = True
                    state["execution_status"] = "failed"
                    state["error_message"] = error_msg
                    state["history"].append({
                        "action": "supervisor_app_start",
                        "app_package": app_package,
                        "execution_round": execution_round,
                        "reason": "app_start_failed",
                        "error_message": error_msg,
                        "timestamp": datetime.now().isoformat(),
                        "status": "error"
                    })
                    return state
        else:
            # 跳过前台检查的情况说明
            if is_first_execution and app_restarted_successfully:
                skip_reason = "App already restarted successfully, skipping redundant foreground check"
                logger.info(f"[task_id: {task_id}] ⏭️ {skip_reason}")
            else:
                skip_reason = "App foreground check skipped for non-first execution with app_foreground_check=False"
                logger.info(f"[task_id: {task_id}] ⏭️ {skip_reason}")

            state["history"].append({
                "action": "supervisor_app_foreground_check_skipped",
                "app_package": app_package,
                "execution_round": execution_round,
                "message": skip_reason,
                "app_restarted": app_restarted_successfully,
                "timestamp": datetime.now().isoformat(),
                "status": "skipped"
            })

        # 2. 检查执行动作数量限制
        execution_limit_reached = self._check_execution_limits(state)
        if execution_limit_reached:
            # 获取配置的倍数用于错误消息
            try:
                config = get_or_create_settings_ins()
                multiplier = config.paths.execution_limit_multiplier
                error_message = f"Task failed due to exceeding execution limit (steps * {multiplier})"
            except:
                error_message = "Task failed due to exceeding execution limit"
            logger.info(f"[task_id: {task_id}] ❌ Execution limit reached, task failed")

            # 记录监督agent终止日志到execution_log
            self._log_supervisor_termination(task_id, "执行次数超限", error_message)

            state["completed"] = True
            state["execution_status"] = "failed"
            state["error_message"] = error_message
            state["history"].append({
                "action": "supervisor_execution_limit_reached",
                "reason": "execution_limit_exceeded",
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            })

        return state

    @staticmethod
    def _log_supervisor_termination(task_id: str, reason: str, error_message: str):
        """
        记录监督agent强制终止的日志到execution_log

        Args:
            task_id: 任务ID
            reason: 终止原因
            error_message: 错误信息
        """
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service

            # 创建监督agent终止日志
            from src.domain.ui_task.mobile.service.execution_log_service import LogRole, LogLevel
            supervisor_log = ExecutionLogService.create_log_entry(
                role=LogRole.SYSTEM,
                message=f"监督Agent强制终止执行: {reason} - {error_message}",
                log_level=LogLevel.ERROR
            )

            # 追加到execution_log
            task_persistence_service.append_execution_log_entries(task_id, [supervisor_log])
            logger.info(f"[{task_id}] 📝 Supervisor termination logged to execution_log: {reason}")

        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to log supervisor termination: {str(e)}")

    @staticmethod
    def _check_app_installed(
            device: str, app_package: str, task_id: str) -> bool:
        """
        检查应用是否已安装

        Args:
            device: 设备ID
            app_package: 应用包名
            task_id: 任务ID

        Returns:
            应用是否已安装
        """
        try:
            _, result = NativeADBUtils.shell_command(device, "pm list packages", timeout=10)

            if result and app_package in result:
                return True
            else:
                logger.error(f"[task_id: {task_id}] ❌ App {app_package} is not installed")
                return False

        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Error checking app installation: {str(e)}")
            return False

    @staticmethod
    def check_device_connection(device: str, task_id: str) -> bool:
        """
        检查设备是否连接

        Args:
            device: 设备ID
            task_id: 任务ID

        Returns:
            设备是否连接
        """
        try:
            # 使用adbutils检查设备连接状态
            return is_device_available(device, task_id)

        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Error checking device connection: {str(e)}")
            return False

    @staticmethod
    def _get_foreground_app(device: str, task_id: str) -> str:
        """
        获取当前前台应用包名

        Args:
            device: 设备ID

        Returns:
            前台应用包名
        """
        import time

        max_retries = 3
        retry_delay = 2  # 每次重试间隔2秒

        for attempt in range(max_retries):
            try:
                # 使用execute_adb_command让管道在宿主机执行，而不是在Android设备内执行
                command = f"adb -s {device} shell dumpsys window | grep mCurrentFocus"
                success, result = NativeADBUtils.execute_adb_command(command, timeout=10)

                if success and result:
                    # 检查结果中是否有有效的窗口信息（不是null的行）
                    lines = result.strip().split('\n')
                    valid_lines = [line for line in lines if line.strip() and 'mCurrentFocus=Window{' in line]

                    if valid_lines:
                        # 有有效的窗口信息，返回完整结果
                        logger.info(f"[task_id: {task_id}] ✓ Current foreground app: {result}")
                        return result
                    elif any('mCurrentFocus=null' in line for line in lines):
                        # 只有null结果，需要重试
                        if attempt < max_retries - 1:
                            logger.warning(f"[task_id: {task_id}] ⚠️ Got mCurrentFocus=null, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                            time.sleep(retry_delay)
                            continue
                        else:
                            logger.warning(f"[task_id: {task_id}] ⚠️ Still got mCurrentFocus=null after {max_retries} attempts")
                            return result
                else:
                    logger.warning(f"[task_id: {task_id}] ⚠️ Empty or failed result, retrying...")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        return "unknown"
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"[task_id: {task_id}] ⚠️ Error getting foreground app (attempt {attempt + 1}/{max_retries}): {str(e)}, retrying...")
                    time.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"[task_id: {task_id}] ❌ Error getting foreground app after {max_retries} attempts: {str(e)}")
                    return "unknown"

        return "unknown"

    @staticmethod
    def _kill_app_process(device: str, app_package: str, task_id: str) -> Tuple[bool, str]:
        """
        杀掉app进程

        Args:
            device: 设备ID
            app_package: 应用包名
            task_id: 任务ID

        Returns:
            (成功标志, 消息)
        """
        try:
            logger.info(f"[task_id: {task_id}] ✓ App {app_package} force-stopped successfully")
            return NativeADBUtils.shell_command(device, f"am force-stop {app_package}", timeout=10)
        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Error killing app process: {str(e)}")
            return False, f"error: {str(e)}"

    @staticmethod
    def _restart_app_to_home(device: str, app_package: str, task_id: str) -> Tuple[bool, str]:
        """
        重启app并确保回到首页状态

        Args:
            device: 设备ID
            app_package: 应用包名
            task_id: 任务ID

        Returns:
            (成功标志, 消息)
        """
        try:
            # 步骤1: 杀掉app进程
            kill_success, kill_message = SupervisorAgent._kill_app_process(device, app_package, task_id)
            if not kill_success:
                return False, f"Failed to kill app: {kill_message}"

            time.sleep(2)

            # 步骤2: 启动app到首页
            start_success, start_method = SupervisorAgent._switch_to_target_app(device, app_package, task_id)
            if not start_success:
                return False, f"Failed to start app: {start_method}"

            # 等待app启动
            time.sleep(5)

            # 步骤3: 验证app是否在前台
            current_app = SupervisorAgent._get_foreground_app(device, task_id)
            if current_app and app_package in current_app:
                logger.info(f"[task_id: {task_id}] ✓ App {app_package} restarted and in foreground")
                return True, f"restart_success_via_{start_method}"
            else:
                logger.warning(f"[task_id: {task_id}] ⚠️ App {app_package} restarted but may not be in foreground")
                return True, f"restart_partial_success_via_{start_method}"

        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Error restarting app: {str(e)}")
            return False, f"error: {str(e)}"

    @staticmethod
    def _switch_to_target_app(device: str, app_package: str, task_id: str) -> Tuple[bool, str]:
        """
        切换到目标应用（当其他应用在前台时）

        Args:
            device: 设备ID
            app_package: 应用包名
            task_id: 任务ID

        Returns:
            (成功标志, 使用的方法)
        """
        try:
            # 方法1: 使用 am start 启动应用（优先使用，更可靠）
            try:
                logger.info(f"[task_id: {task_id}] 🚀 Trying to start app using am start command")
                # 首先尝试通用的启动方式
                success, output = NativeADBUtils.shell_command(device, f"am start -n {app_package}/.client.BlankActivity", timeout=15)
                if success and "Error" not in output:
                    logger.info(f"[task_id: {task_id}] ✓ App started successfully using BlankActivity")
                    return True, "am_start_blank_activity"
                # 如果BlankActivity失败，尝试MainActivity
                success, output = NativeADBUtils.shell_command(device, f"am start -n {app_package}/.MainActivity", timeout=15)
                if success and "Error" not in output:
                    logger.info(f"[task_id: {task_id}] ✓ App started successfully using MainActivity")
                    return True, "am_start_main_activity"
                # 如果具体Activity失败，尝试包名启动
                success, output = NativeADBUtils.shell_command(device, f"am start {app_package}", timeout=15)
                if success and "Error" not in output:
                    logger.info(f"[task_id: {task_id}] ✓ App started successfully using package name")
                    return True, "am_start_package"
                logger.warning(f"[task_id: {task_id}] ⚠️ am start methods failed, output: {output}")
            except Exception as e:
                logger.error(f"[task_id: {task_id}] ❌ App start using am start failed: {str(e)}")
            logger.error(f"[task_id: {task_id}] ❌ All app start methods failed")
            return False, "all_start_methods_failed"
        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Error in switch_to_target_app: {str(e)}")
            return False, f"error: {str(e)}"

    @staticmethod
    def _check_execution_limits(state: DeploymentState) -> bool:
        """
        检查执行动作数量限制
        根据配置文件中的倍数设置限制，超过即失败

        Args:
            state: 当前状态

        Returns:
            是否达到执行限制
        """
        try:
            # 获取配置
            config = get_or_create_settings_ins()
            execution_limit_multiplier = config.paths.execution_limit_multiplier

            # 获取当前执行的动作数量 - 只计算实际的决策+执行动作
            history = state.get("history", [])
            # 只计算 enhanced_get_location 类型的记录，这代表实际的决策+执行动作
            action_count = len([h for h in history if h.get("action") == "enhanced_get_location"])

            # 获取测试用例步骤数量
            task_steps = state.get("task_steps", [])
            total_steps = len(task_steps)

            # 计算执行限制：步骤数 * 配置的倍数
            action_limit = total_steps * execution_limit_multiplier

            task_id = state["task_id"]
            logger.info(f"[task_id: {task_id}] 📊 Supervisor check: {action_count}/{action_limit} actions executed (multiplier: {execution_limit_multiplier})")

            # 检查是否达到限制
            if action_count >= action_limit:
                logger.info(f"[task_id: {task_id}] ❌ Action limit reached: {action_count}/{action_limit} actions executed")
                return True

            return False

        except Exception as e:
            task_id = state.get("task_id", "unknown")
            logger.error(f"[{task_id}] ❌ Error checking execution limits: {str(e)}")

            # 使用统一的异常处理方法
            TaskExceptionHandler.update_task_status_to_failed(
                task_id,
                f"Execution limits check failed: {str(e)}"
            )

            # 检查失败时保守地返回True（认为达到限制）
            return True